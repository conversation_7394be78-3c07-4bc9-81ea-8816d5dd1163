# æå¡å¨éç½®
server.port=8888
server.servlet.context-path=/api

# æ°æ®åºéç½®
spring.datasource.url=***************************************************************************************************
spring.datasource.username=root
spring.datasource.password=123456
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JPAéç½®
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false

# Jacksonéç½®
spring.jackson.serialization.fail-on-empty-beans=false
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8

# JWTéç½®
biaozhu.app.jwtSecret=biaozhuSecretKey
biaozhu.app.jwtExpirationMs=86400000

# æä»¶ä¸ä¼ éç½®
spring.servlet.multipart.max-file-size=50MB
spring.servlet.multipart.max-request-size=50MB
file.upload-dir=./uploads

# æ¥å¿éç½®
logging.level.org.springframework.web=DEBUG
logging.level.org.springframework.web.filter.CommonsRequestLoggingFilter=DEBUG
logging.level.org.hibernate=ERROR
logging.level.com.example.biaozhu=DEBUG

# è¯·æ±æ¥å¿éç½®
spring.mvc.log-request-details=true
logging.level.web=DEBUG

# è·¨åéç½®
cors.allowed-origins=*
cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
cors.allowed-headers=Authorization,Content-Type

# Swagger/Springfoxéç½®
spring.mvc.pathmatch.matching-strategy=ANT_PATH_MATCHER 