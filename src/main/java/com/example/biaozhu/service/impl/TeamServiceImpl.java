package com.example.biaozhu.service.impl;

import com.example.biaozhu.entity.Project;
import com.example.biaozhu.entity.Team;
import com.example.biaozhu.entity.User;
import com.example.biaozhu.exception.ResourceNotFoundException;
import com.example.biaozhu.payload.request.TeamRequest;
import com.example.biaozhu.repository.ProjectRepository;
import com.example.biaozhu.repository.TeamRepository;
import com.example.biaozhu.repository.UserRepository;
import com.example.biaozhu.service.TeamService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 团队服务实现类
 */
@Service
public class TeamServiceImpl implements TeamService {
    
    private final TeamRepository teamRepository;
    private final UserRepository userRepository;
    private final ProjectRepository projectRepository;
    
    @Autowired
    public TeamServiceImpl(TeamRepository teamRepository, UserRepository userRepository, ProjectRepository projectRepository) {
        this.teamRepository = teamRepository;
        this.userRepository = userRepository;
        this.projectRepository = projectRepository;
    }
    
    @Override
    @Transactional
    public Team createTeam(TeamRequest teamRequest, User creator) {
        // 检查团队名称是否已存在
        if (teamRepository.existsByName(teamRequest.getName())) {
            throw new IllegalArgumentException("团队名称已存在：" + teamRequest.getName());
        }
        
        // 创建新团队
        Team team = new Team();
        team.setName(teamRequest.getName());
        team.setDescription(teamRequest.getDescription());
        team.setCreator(creator);
        team.setStatus(Team.TeamStatus.ACTIVE);
        
        // 创建者自动成为成员和管理员
        team.addMember(creator);
        team.addAdmin(creator);
        
        // 添加其他成员
        if (teamRequest.getMemberIds() != null && !teamRequest.getMemberIds().isEmpty()) {
            for (Long userId : teamRequest.getMemberIds()) {
                User member = userRepository.findById(userId)
                        .orElseThrow(() -> new ResourceNotFoundException("未找到ID为 " + userId + " 的用户"));
                team.addMember(member);
            }
        }
        
        // 添加其他管理员
        if (teamRequest.getAdminIds() != null && !teamRequest.getAdminIds().isEmpty()) {
            for (Long userId : teamRequest.getAdminIds()) {
                User admin = userRepository.findById(userId)
                        .orElseThrow(() -> new ResourceNotFoundException("未找到ID为 " + userId + " 的用户"));
                team.addAdmin(admin);
            }
        }
        
        // 添加项目
        if (teamRequest.getProjectIds() != null && !teamRequest.getProjectIds().isEmpty()) {
            for (Long projectId : teamRequest.getProjectIds()) {
                Project project = projectRepository.findById(projectId)
                        .orElseThrow(() -> new ResourceNotFoundException("未找到ID为 " + projectId + " 的项目"));
                team.addProject(project);
            }
        }
        
        // 保存并返回新团队
        return teamRepository.save(team);
    }
    
    @Override
    @Transactional
    public Team updateTeam(Long id, TeamRequest teamRequest) {
        Team team = getTeamById(id);
        
        // 更新团队名称（检查是否与其他团队重名）
        if (!team.getName().equals(teamRequest.getName()) && 
            teamRepository.existsByName(teamRequest.getName())) {
            throw new IllegalArgumentException("团队名称已存在：" + teamRequest.getName());
        }
        
        team.setName(teamRequest.getName());
        team.setDescription(teamRequest.getDescription());
        
        // 更新成员列表
        if (teamRequest.getMemberIds() != null) {
            // 获取现有成员ID集合
            Set<Long> existingMemberIds = team.getMembers().stream()
                    .map(User::getId)
                    .collect(Collectors.toSet());
            
            // 获取管理员ID集合，管理员不能从成员列表中移除
            Set<Long> adminIds = team.getAdmins().stream()
                    .map(User::getId)
                    .collect(Collectors.toSet());
            
            // 要添加的成员
            Set<Long> toAddIds = teamRequest.getMemberIds().stream()
                    .filter(memberId -> !existingMemberIds.contains(memberId))
                    .collect(Collectors.toSet());
            
            // 要移除的成员（排除管理员）
            Set<Long> toRemoveIds = existingMemberIds.stream()
                    .filter(memberId -> !teamRequest.getMemberIds().contains(memberId) && !adminIds.contains(memberId))
                    .collect(Collectors.toSet());
            
            // 添加新成员
            for (Long memberUserId : toAddIds) {
                User member = userRepository.findById(memberUserId)
                        .orElseThrow(() -> new ResourceNotFoundException("未找到ID为 " + memberUserId + " 的用户"));
                team.addMember(member);
            }
            
            // 移除不再是成员的用户
            for (Long memberUserId : toRemoveIds) {
                User member = userRepository.findById(memberUserId)
                        .orElseThrow(() -> new ResourceNotFoundException("未找到ID为 " + memberUserId + " 的用户"));
                team.removeMember(member);
            }
        }
        
        // 更新管理员列表
        if (teamRequest.getAdminIds() != null) {
            // 确保创建者始终是管理员
            if (!teamRequest.getAdminIds().contains(team.getCreator().getId())) {
                teamRequest.getAdminIds().add(team.getCreator().getId());
            }
            
            // 获取现有管理员ID集合
            Set<Long> existingAdminIds = team.getAdmins().stream()
                    .map(User::getId)
                    .collect(Collectors.toSet());
            
            // 要添加的管理员
            Set<Long> toAddAdminIds = teamRequest.getAdminIds().stream()
                    .filter(adminId -> !existingAdminIds.contains(adminId))
                    .collect(Collectors.toSet());
            
            // 要移除的管理员（排除创建者）
            Set<Long> toRemoveAdminIds = existingAdminIds.stream()
                    .filter(adminId -> !teamRequest.getAdminIds().contains(adminId) && !adminId.equals(team.getCreator().getId()))
                    .collect(Collectors.toSet());
            
            // 添加新管理员
            for (Long adminUserId : toAddAdminIds) {
                User admin = userRepository.findById(adminUserId)
                        .orElseThrow(() -> new ResourceNotFoundException("未找到ID为 " + adminUserId + " 的用户"));
                team.addAdmin(admin);
            }
            
            // 移除不再是管理员的用户
            for (Long adminUserId : toRemoveAdminIds) {
                User admin = userRepository.findById(adminUserId)
                        .orElseThrow(() -> new ResourceNotFoundException("未找到ID为 " + adminUserId + " 的用户"));
                team.removeAdmin(admin);
            }
        }
        
        // 更新项目列表
        if (teamRequest.getProjectIds() != null) {
            // 获取现有项目ID集合
            Set<Long> existingProjectIds = team.getProjects().stream()
                    .map(Project::getId)
                    .collect(Collectors.toSet());
            
            // 要添加的项目
            Set<Long> toAddProjectIds = teamRequest.getProjectIds().stream()
                    .filter(projectId -> !existingProjectIds.contains(projectId))
                    .collect(Collectors.toSet());
            
            // 要移除的项目
            Set<Long> toRemoveProjectIds = existingProjectIds.stream()
                    .filter(projectId -> !teamRequest.getProjectIds().contains(projectId))
                    .collect(Collectors.toSet());
            
            // 添加新项目
            for (Long projectId : toAddProjectIds) {
                Project project = projectRepository.findById(projectId)
                        .orElseThrow(() -> new ResourceNotFoundException("未找到ID为 " + projectId + " 的项目"));
                team.addProject(project);
            }
            
            // 移除不再关联的项目
            for (Long projectId : toRemoveProjectIds) {
                Project project = projectRepository.findById(projectId)
                        .orElseThrow(() -> new ResourceNotFoundException("未找到ID为 " + projectId + " 的项目"));
                team.removeProject(project);
            }
        }
        
        // 保存并返回更新后的团队
        return teamRepository.save(team);
    }
    
    @Override
    @Transactional
    public void deleteTeam(Long id) {
        Team team = getTeamById(id);
        teamRepository.delete(team);
    }
    
    @Override
    public Team getTeamById(Long id) {
        return teamRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("未找到ID为 " + id + " 的团队"));
    }
    
    @Override
    public Page<Team> getAllTeams(int page, int size, String sort) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, sort));
        return teamRepository.findAll(pageable);
    }
    
    @Override
    public Page<Team> getTeamsByCreator(User user, int page, int size, String sort) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, sort));
        return teamRepository.findByCreator(user, pageable);
    }
    
    @Override
    public Page<Team> getTeamsByMember(User user, int page, int size, String sort) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, sort));
        return teamRepository.findByMember(user, pageable);
    }
    
    @Override
    public Page<Team> getTeamsByAdmin(User user, int page, int size, String sort) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, sort));
        return teamRepository.findByAdmin(user, pageable);
    }
    
    @Override
    public Page<Team> searchTeams(String keyword, int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));
        return teamRepository.searchByKeyword(keyword, pageable);
    }
    
    @Override
    @Transactional
    public Team addMember(Long teamId, Long userId) {
        Team team = getTeamById(teamId);
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("未找到ID为 " + userId + " 的用户"));
        
        team.addMember(user);
        return teamRepository.save(team);
    }
    
    @Override
    @Transactional
    public Team removeMember(Long teamId, Long userId) {
        Team team = getTeamById(teamId);
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("未找到ID为 " + userId + " 的用户"));
        
        // 检查用户是否是管理员，管理员必须先移除管理员权限
        if (team.isAdmin(user)) {
            throw new IllegalArgumentException("请先移除用户的管理员权限");
        }
        
        // 检查用户是否是创建者，创建者不能被移除
        if (team.getCreator().equals(user)) {
            throw new IllegalArgumentException("团队创建者不能被移出团队");
        }
        
        team.removeMember(user);
        return teamRepository.save(team);
    }
    
    @Override
    @Transactional
    public Team addAdmin(Long teamId, Long userId) {
        Team team = getTeamById(teamId);
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("未找到ID为 " + userId + " 的用户"));
        
        team.addAdmin(user);
        return teamRepository.save(team);
    }
    
    @Override
    @Transactional
    public Team removeAdmin(Long teamId, Long userId) {
        Team team = getTeamById(teamId);
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("未找到ID为 " + userId + " 的用户"));
        
        // 检查用户是否是创建者，创建者不能被移除管理员权限
        if (team.getCreator().equals(user)) {
            throw new IllegalArgumentException("团队创建者不能被移除管理员权限");
        }
        
        team.removeAdmin(user);
        return teamRepository.save(team);
    }
    
    @Override
    @Transactional
    public Team addProject(Long teamId, Long projectId) {
        Team team = getTeamById(teamId);
        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new ResourceNotFoundException("未找到ID为 " + projectId + " 的项目"));
        
        team.addProject(project);
        return teamRepository.save(team);
    }
    
    @Override
    @Transactional
    public Team removeProject(Long teamId, Long projectId) {
        Team team = getTeamById(teamId);
        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new ResourceNotFoundException("未找到ID为 " + projectId + " 的项目"));
        
        team.removeProject(project);
        return teamRepository.save(team);
    }
    
    @Override
    public Map<String, Object> getTeamStatistics(Long id) {
        Team team = getTeamById(id);
        
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("memberCount", team.getMembers().size());
        statistics.put("adminCount", team.getAdmins().size());
        statistics.put("projectCount", team.getProjects().size());
        
        return statistics;
    }
    
    @Override
    public Map<String, Long> getTeamStatusStatistics() {
        List<Object[]> results = teamRepository.countByStatus();
        Map<String, Long> statistics = new HashMap<>();
        
        for (Object[] result : results) {
            Team.TeamStatus status = (Team.TeamStatus) result[0];
            Long count = ((Number) result[1]).longValue();
            statistics.put(status.name(), count);
        }
        
        return statistics;
    }
    
    @Override
    public List<Team> getTopTeamsByMemberCount(int limit) {
        return teamRepository.findTopTeamsByMemberCount(limit);
    }
    
    @Override
    public boolean isTeamCreator(Long teamId, Long userId) {
        Team team = getTeamById(teamId);
        return team.getCreator().getId().equals(userId);
    }
    
    @Override
    public boolean isTeamAdmin(Long teamId, Long userId) {
        Team team = getTeamById(teamId);
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("未找到ID为 " + userId + " 的用户"));
        
        return team.isAdmin(user);
    }
    
    @Override
    public boolean isTeamMember(Long teamId, Long userId) {
        Team team = getTeamById(teamId);
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("未找到ID为 " + userId + " 的用户"));
        
        return team.isMember(user);
    }
    
    @Override
    @Transactional
    public Team updateTeamStatus(Long id, Team.TeamStatus status) {
        Team team = getTeamById(id);
        team.setStatus(status);
        return teamRepository.save(team);
    }
} 