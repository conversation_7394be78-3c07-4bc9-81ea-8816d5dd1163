package com.example.biaozhu.controller;

import com.example.biaozhu.dto.SystemLogDTO;
import com.example.biaozhu.payload.request.LogQueryRequest;
import com.example.biaozhu.payload.response.MessageResponse;
import com.example.biaozhu.service.SystemLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 系统日志控制器
 * 处理系统日志查询和管理相关请求
 */
@RestController
@RequestMapping("/api/system-logs")
@PreAuthorize("hasRole('ADMIN')")  // 只有管理员可以查看系统日志
public class SystemLogController {

    private final SystemLogService systemLogService;

    @Autowired
    public SystemLogController(SystemLogService systemLogService) {
        this.systemLogService = systemLogService;
    }

    /**
     * 获取所有系统日志（分页）
     *
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @param sort 排序字段
     * @param direction 排序方向
     * @return 分页日志列表
     */
    @GetMapping
    public ResponseEntity<Map<String, Object>> getAllLogs(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt") String sort,
            @RequestParam(defaultValue = "desc") String direction) {

        Sort.Direction sortDirection = direction.equalsIgnoreCase("asc") ? Sort.Direction.ASC : Sort.Direction.DESC;
        Pageable pageable = PageRequest.of(page, size, Sort.by(sortDirection, sort));
        
        Page<SystemLogDTO> logPage = systemLogService.findAllLogs(pageable);
        
        Map<String, Object> response = new HashMap<>();
        response.put("logs", logPage.getContent());
        response.put("currentPage", logPage.getNumber());
        response.put("totalItems", logPage.getTotalElements());
        response.put("totalPages", logPage.getTotalPages());
        
        return ResponseEntity.ok(response);
    }

    /**
     * 根据操作类型获取日志
     *
     * @param operation 操作类型
     * @param page 页码
     * @param size 每页大小
     * @return 分页日志列表
     */
    @GetMapping("/operation/{operation}")
    public ResponseEntity<Map<String, Object>> getLogsByOperation(
            @PathVariable String operation,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));
        Page<SystemLogDTO> logPage = systemLogService.findByOperation(operation, pageable);
        
        Map<String, Object> response = new HashMap<>();
        response.put("logs", logPage.getContent());
        response.put("currentPage", logPage.getNumber());
        response.put("totalItems", logPage.getTotalElements());
        response.put("totalPages", logPage.getTotalPages());
        
        return ResponseEntity.ok(response);
    }

    /**
     * 根据用户名获取日志
     *
     * @param username 用户名
     * @param page 页码
     * @param size 每页大小
     * @return 分页日志列表
     */
    @GetMapping("/user/{username}")
    public ResponseEntity<Map<String, Object>> getLogsByUsername(
            @PathVariable String username,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));
        Page<SystemLogDTO> logPage = systemLogService.findByUsername(username, pageable);
        
        Map<String, Object> response = new HashMap<>();
        response.put("logs", logPage.getContent());
        response.put("currentPage", logPage.getNumber());
        response.put("totalItems", logPage.getTotalElements());
        response.put("totalPages", logPage.getTotalPages());
        
        return ResponseEntity.ok(response);
    }

    /**
     * 根据时间范围获取日志
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param page 页码
     * @param size 每页大小
     * @return 分页日志列表
     */
    @GetMapping("/time-range")
    public ResponseEntity<Map<String, Object>> getLogsByTimeRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));
        Page<SystemLogDTO> logPage = systemLogService.findByTimeRange(startTime, endTime, pageable);
        
        Map<String, Object> response = new HashMap<>();
        response.put("logs", logPage.getContent());
        response.put("currentPage", logPage.getNumber());
        response.put("totalItems", logPage.getTotalElements());
        response.put("totalPages", logPage.getTotalPages());
        
        return ResponseEntity.ok(response);
    }

    /**
     * 根据日志级别获取日志
     *
     * @param logLevel 日志级别
     * @param page 页码
     * @param size 每页大小
     * @return 分页日志列表
     */
    @GetMapping("/level/{logLevel}")
    public ResponseEntity<Map<String, Object>> getLogsByLogLevel(
            @PathVariable String logLevel,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));
        Page<SystemLogDTO> logPage = systemLogService.findByLogLevel(logLevel, pageable);
        
        Map<String, Object> response = new HashMap<>();
        response.put("logs", logPage.getContent());
        response.put("currentPage", logPage.getNumber());
        response.put("totalItems", logPage.getTotalElements());
        response.put("totalPages", logPage.getTotalPages());
        
        return ResponseEntity.ok(response);
    }

    /**
     * 根据模块获取日志
     *
     * @param module 模块名称
     * @param page 页码
     * @param size 每页大小
     * @return 分页日志列表
     */
    @GetMapping("/module/{module}")
    public ResponseEntity<Map<String, Object>> getLogsByModule(
            @PathVariable String module,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));
        Page<SystemLogDTO> logPage = systemLogService.findByModule(module, pageable);
        
        Map<String, Object> response = new HashMap<>();
        response.put("logs", logPage.getContent());
        response.put("currentPage", logPage.getNumber());
        response.put("totalItems", logPage.getTotalElements());
        response.put("totalPages", logPage.getTotalPages());
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 根据复合条件查询日志
     *
     * @param queryRequest 查询条件
     * @return 分页日志列表
     */
    @PostMapping("/search")
    public ResponseEntity<Map<String, Object>> searchLogs(@RequestBody LogQueryRequest queryRequest) {
        Sort.Direction sortDirection = Sort.Direction.fromString(queryRequest.getDirection());
        Pageable pageable = PageRequest.of(
                queryRequest.getPage(), 
                queryRequest.getSize(), 
                Sort.by(sortDirection, queryRequest.getSort())
        );
        
        Page<SystemLogDTO> logPage = systemLogService.findByConditions(
                queryRequest.getUsername(),
                queryRequest.getOperation(),
                queryRequest.getLogLevel(),
                queryRequest.getModule(),
                queryRequest.getStartTime(),
                queryRequest.getEndTime(),
                pageable
        );
        
        Map<String, Object> response = new HashMap<>();
        response.put("logs", logPage.getContent());
        response.put("currentPage", logPage.getNumber());
        response.put("totalItems", logPage.getTotalElements());
        response.put("totalPages", logPage.getTotalPages());
        
        return ResponseEntity.ok(response);
    }

    /**
     * 获取操作类型统计
     *
     * @return 操作类型统计信息
     */
    @GetMapping("/statistics/operation")
    public ResponseEntity<Map<String, Long>> getOperationStatistics() {
        Map<String, Long> statistics = systemLogService.getOperationStatistics();
        return ResponseEntity.ok(statistics);
    }

    /**
     * 获取日志级别统计
     *
     * @return 日志级别统计信息
     */
    @GetMapping("/statistics/log-level")
    public ResponseEntity<Map<String, Long>> getLogLevelStatistics() {
        Map<String, Long> statistics = systemLogService.getLogLevelStatistics();
        return ResponseEntity.ok(statistics);
    }

    /**
     * 获取用户操作统计
     *
     * @return 用户操作统计信息
     */
    @GetMapping("/statistics/user-operation")
    public ResponseEntity<Map<String, Long>> getUserOperationStatistics() {
        Map<String, Long> statistics = systemLogService.getUserOperationStatistics();
        return ResponseEntity.ok(statistics);
    }
    
    /**
     * 获取日志趋势
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param interval 时间间隔（day, hour）
     * @return 日志趋势数据
     */
    @GetMapping("/trend")
    public ResponseEntity<List<Map<String, Object>>> getLogTrend(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
            @RequestParam(defaultValue = "day") String interval) {
        
        List<Map<String, Object>> trend = systemLogService.getLogTrend(startTime, endTime, interval);
        return ResponseEntity.ok(trend);
    }

    /**
     * 获取最近的错误日志
     *
     * @param limit 限制条数
     * @return 错误日志列表
     */
    @GetMapping("/recent-errors")
    public ResponseEntity<List<SystemLogDTO>> getRecentErrors(
            @RequestParam(defaultValue = "10") int limit) {
        List<SystemLogDTO> errorLogs = systemLogService.findRecentErrorLogs(limit);
        return ResponseEntity.ok(errorLogs);
    }

    /**
     * 清除过期日志
     *
     * @param days 保留天数
     * @return 操作结果
     */
    @DeleteMapping("/cleanup")
    public ResponseEntity<MessageResponse> cleanupLogs(
            @RequestParam(defaultValue = "90") int days) {
        int deletedCount = systemLogService.cleanupLogs(days);
        return ResponseEntity.ok(new MessageResponse("成功清除 " + deletedCount + " 条过期日志"));
    }
} 