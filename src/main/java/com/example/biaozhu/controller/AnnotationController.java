package com.example.biaozhu.controller;

import com.example.biaozhu.entity.Annotation;
import com.example.biaozhu.entity.User;
import com.example.biaozhu.payload.request.AnnotationRequest;
import com.example.biaozhu.payload.response.AnnotationResponse;
import com.example.biaozhu.payload.response.MessageResponse;
import com.example.biaozhu.service.AnnotationService;
import com.example.biaozhu.service.UserService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 标注控制器
 * 处理数据标注相关请求
 */
@RestController
@RequestMapping("/api/annotations")
public class AnnotationController {

    private final AnnotationService annotationService;
    private final UserService userService;
    
    @Autowired
    public AnnotationController(AnnotationService annotationService, UserService userService) {
        this.annotationService = annotationService;
        this.userService = userService;
    }
    
    /**
     * 创建新标注
     * 
     * @param annotationRequest 标注请求对象
     * @return 创建的标注信息
     */
    @PostMapping
    public ResponseEntity<?> createAnnotation(@Valid @RequestBody AnnotationRequest annotationRequest) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        User currentUser = userService.getUserByUsername(authentication.getName());
        
        Annotation annotation = annotationService.createAnnotation(annotationRequest, currentUser);
        return new ResponseEntity<>(new AnnotationResponse(annotation), HttpStatus.CREATED);
    }
    
    /**
     * 批量创建标注
     * 
     * @param annotationRequests 标注请求对象列表
     * @return 创建结果消息
     */
    @PostMapping("/batch")
    public ResponseEntity<?> createAnnotationsBatch(@Valid @RequestBody List<AnnotationRequest> annotationRequests) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        User currentUser = userService.getUserByUsername(authentication.getName());
        
        List<Annotation> annotations = annotationService.createAnnotationsBatch(annotationRequests, currentUser);
        return new ResponseEntity<>(
                new MessageResponse("成功创建 " + annotations.size() + " 条标注"),
                HttpStatus.CREATED);
    }
    
    /**
     * 获取所有标注（分页）
     * 
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序字段
     * @return 标注分页数据
     */
    @GetMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> getAllAnnotations(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sort) {
        
        Page<Annotation> annotationsPage = annotationService.getAllAnnotations(page, size, sort);
        
        Map<String, Object> response = new HashMap<>();
        response.put("annotations", annotationsPage.getContent().stream()
                .map(AnnotationResponse::new)
                .collect(Collectors.toList()));
        response.put("currentPage", annotationsPage.getNumber());
        response.put("totalItems", annotationsPage.getTotalElements());
        response.put("totalPages", annotationsPage.getTotalPages());
        
        return new ResponseEntity<>(response, HttpStatus.OK);
    }
    
    /**
     * 获取用户的所有标注
     * 
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序字段
     * @return 标注分页数据
     */
    @GetMapping("/user/{userId}")
    @PreAuthorize("hasRole('ADMIN') or authentication.principal.id == #userId")
    public ResponseEntity<?> getAnnotationsByUser(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sort) {
        
        Page<Annotation> annotationsPage = annotationService.getAnnotationsByUser(userId, page, size, sort);
        
        Map<String, Object> response = new HashMap<>();
        response.put("annotations", annotationsPage.getContent().stream()
                .map(AnnotationResponse::new)
                .collect(Collectors.toList()));
        response.put("currentPage", annotationsPage.getNumber());
        response.put("totalItems", annotationsPage.getTotalElements());
        response.put("totalPages", annotationsPage.getTotalPages());
        
        return new ResponseEntity<>(response, HttpStatus.OK);
    }
    
    /**
     * 获取当前用户的所有标注
     * 
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序字段
     * @return 标注分页数据
     */
    @GetMapping("/me")
    public ResponseEntity<?> getMyAnnotations(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sort) {
        
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        User currentUser = userService.getUserByUsername(authentication.getName());
        
        Page<Annotation> annotationsPage = annotationService.getAnnotationsByUser(currentUser.getId(), page, size, sort);
        
        Map<String, Object> response = new HashMap<>();
        response.put("annotations", annotationsPage.getContent().stream()
                .map(AnnotationResponse::new)
                .collect(Collectors.toList()));
        response.put("currentPage", annotationsPage.getNumber());
        response.put("totalItems", annotationsPage.getTotalElements());
        response.put("totalPages", annotationsPage.getTotalPages());
        
        return new ResponseEntity<>(response, HttpStatus.OK);
    }
    
    /**
     * 获取任务的所有标注
     * 
     * @param taskId 任务ID
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序字段
     * @return 标注分页数据
     */
    @GetMapping("/task/{taskId}")
    @PreAuthorize("@taskService.isTaskMember(#taskId) or hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<?> getAnnotationsByTask(
            @PathVariable Long taskId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sort) {
        
        Page<Annotation> annotationsPage = annotationService.getAnnotationsByTask(taskId, page, size, sort);
        
        Map<String, Object> response = new HashMap<>();
        response.put("annotations", annotationsPage.getContent().stream()
                .map(AnnotationResponse::new)
                .collect(Collectors.toList()));
        response.put("currentPage", annotationsPage.getNumber());
        response.put("totalItems", annotationsPage.getTotalElements());
        response.put("totalPages", annotationsPage.getTotalPages());
        
        return new ResponseEntity<>(response, HttpStatus.OK);
    }
    
    /**
     * 获取数据项的所有标注
     * 
     * @param dataItemId 数据项ID
     * @return 标注列表
     */
    @GetMapping("/dataitem/{dataItemId}")
    public ResponseEntity<?> getAnnotationsByDataItem(@PathVariable Long dataItemId) {
        List<Annotation> annotations = annotationService.getAnnotationsByDataItem(dataItemId);
        return ResponseEntity.ok(annotations.stream()
                .map(AnnotationResponse::new)
                .collect(Collectors.toList()));
    }
    
    /**
     * 根据ID获取标注
     * 
     * @param id 标注ID
     * @return 标注信息
     */
    @GetMapping("/{id}")
    public ResponseEntity<?> getAnnotationById(@PathVariable Long id) {
        Annotation annotation = annotationService.getAnnotationById(id);
        return ResponseEntity.ok(new AnnotationResponse(annotation));
    }
    
    /**
     * 更新标注
     * 
     * @param id 标注ID
     * @param annotationRequest 标注请求对象
     * @return 更新后的标注信息
     */
    @PutMapping("/{id}")
    @PreAuthorize("@annotationService.isAnnotationCreator(#id) or hasRole('ADMIN')")
    public ResponseEntity<?> updateAnnotation(
            @PathVariable Long id,
            @Valid @RequestBody AnnotationRequest annotationRequest) {
        
        Annotation updatedAnnotation = annotationService.updateAnnotation(id, annotationRequest);
        return ResponseEntity.ok(new AnnotationResponse(updatedAnnotation));
    }
    
    /**
     * 删除标注
     * 
     * @param id 标注ID
     * @return 删除成功响应
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("@annotationService.isAnnotationCreator(#id) or hasRole('ADMIN')")
    public ResponseEntity<?> deleteAnnotation(@PathVariable Long id) {
        annotationService.deleteAnnotation(id);
        return ResponseEntity.ok(new MessageResponse("标注删除成功"));
    }
    
    /**
     * 批量删除标注
     * 
     * @param ids 标注ID列表
     * @return 删除成功响应
     */
    @DeleteMapping("/batch")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> deleteAnnotationsBatch(@RequestBody List<Long> ids) {
        int count = annotationService.deleteAnnotationsBatch(ids);
        return ResponseEntity.ok(new MessageResponse("成功删除 " + count + " 条标注"));
    }
    
    /**
     * 标注审核
     * 
     * @param id 标注ID
     * @param approved 是否通过
     * @param comments 审核意见
     * @return 审核结果消息
     */
    @PutMapping("/{id}/review")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER') or hasRole('REVIEWER')")
    public ResponseEntity<?> reviewAnnotation(
            @PathVariable Long id,
            @RequestParam boolean approved,
            @RequestParam(required = false) String comments) {
        
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        User reviewer = userService.getUserByUsername(authentication.getName());
        
        annotationService.reviewAnnotation(id, reviewer, approved, comments);
        return ResponseEntity.ok(new MessageResponse("标注已" + (approved ? "批准" : "拒绝")));
    }
    
    /**
     * 批量审核标注
     * 
     * @param ids 标注ID列表
     * @param approved 是否通过
     * @param comments 审核意见
     * @return 审核结果消息
     */
    @PutMapping("/batch/review")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER') or hasRole('REVIEWER')")
    public ResponseEntity<?> reviewAnnotationsBatch(
            @RequestBody List<Long> ids,
            @RequestParam boolean approved,
            @RequestParam(required = false) String comments) {
        
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        User reviewer = userService.getUserByUsername(authentication.getName());
        
        int count = annotationService.reviewAnnotationsBatch(ids, reviewer, approved, comments);
        return ResponseEntity.ok(new MessageResponse("成功审核 " + count + " 条标注"));
    }
    
    /**
     * 导出标注数据
     * 
     * @param taskId 任务ID
     * @param format 导出格式
     * @return 导出的数据
     */
    @GetMapping("/export/task/{taskId}")
    @PreAuthorize("@taskService.isTaskMember(#taskId) or hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<?> exportAnnotations(
            @PathVariable Long taskId,
            @RequestParam(defaultValue = "json") String format) {
        
        return annotationService.exportAnnotations(taskId, format);
    }
    
    /**
     * 获取标注统计信息
     * 
     * @param taskId 任务ID
     * @return 标注统计信息
     */
    @GetMapping("/statistics/task/{taskId}")
    @PreAuthorize("@taskService.isTaskMember(#taskId) or hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<?> getAnnotationStatistics(@PathVariable Long taskId) {
        Map<String, Object> statistics = annotationService.getAnnotationStatistics(taskId);
        return ResponseEntity.ok(statistics);
    }
    
    /**
     * 获取用户标注性能统计
     * 
     * @param userId 用户ID
     * @param days 统计天数
     * @return 标注性能统计
     */
    @GetMapping("/performance/user/{userId}")
    @PreAuthorize("authentication.principal.id == #userId or hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<?> getUserAnnotationPerformance(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "30") int days) {
        
        Map<String, Object> performance = annotationService.getUserAnnotationPerformance(userId, days);
        return ResponseEntity.ok(performance);
    }
} 