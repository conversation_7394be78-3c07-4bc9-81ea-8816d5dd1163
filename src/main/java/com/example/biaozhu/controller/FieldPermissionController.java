package com.example.biaozhu.controller;

import com.example.biaozhu.payload.request.FieldPermissionRequest;
import com.example.biaozhu.payload.response.FieldPermissionResponse;
import com.example.biaozhu.payload.response.MessageResponse;
import com.example.biaozhu.service.FieldPermissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 字段权限控制器
 */
@RestController
@RequestMapping("/api/field-permissions")
@PreAuthorize("hasRole('ADMIN')")  // 只有管理员可以管理字段权限
public class FieldPermissionController {

    private final FieldPermissionService fieldPermissionService;

    @Autowired
    public FieldPermissionController(FieldPermissionService fieldPermissionService) {
        this.fieldPermissionService = fieldPermissionService;
    }

    /**
     * 创建字段权限
     */
    @PostMapping
    public ResponseEntity<FieldPermissionResponse> createFieldPermission(
            @Valid @RequestBody FieldPermissionRequest request) {
        
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String currentUsername = authentication.getName();
        
        FieldPermissionResponse permission = fieldPermissionService.createFieldPermission(request, currentUsername);
        return new ResponseEntity<>(permission, HttpStatus.CREATED);
    }

    /**
     * 获取所有字段权限
     */
    @GetMapping
    public ResponseEntity<List<FieldPermissionResponse>> getAllFieldPermissions() {
        List<FieldPermissionResponse> permissions = fieldPermissionService.getAllFieldPermissions();
        return ResponseEntity.ok(permissions);
    }

    /**
     * 根据ID获取字段权限
     */
    @GetMapping("/{id}")
    public ResponseEntity<FieldPermissionResponse> getFieldPermissionById(@PathVariable Long id) {
        FieldPermissionResponse permission = fieldPermissionService.getFieldPermissionById(id);
        return ResponseEntity.ok(permission);
    }

    /**
     * 根据角色ID获取字段权限
     */
    @GetMapping("/role/{roleId}")
    public ResponseEntity<List<FieldPermissionResponse>> getFieldPermissionsByRole(@PathVariable Long roleId) {
        List<FieldPermissionResponse> permissions = fieldPermissionService.getFieldPermissionsByRole(roleId);
        return ResponseEntity.ok(permissions);
    }

    /**
     * 根据实体名称获取字段权限
     */
    @GetMapping("/entity/{entityName}")
    public ResponseEntity<List<FieldPermissionResponse>> getFieldPermissionsByEntity(@PathVariable String entityName) {
        List<FieldPermissionResponse> permissions = fieldPermissionService.getFieldPermissionsByEntity(entityName);
        return ResponseEntity.ok(permissions);
    }

    /**
     * 更新字段权限
     */
    @PutMapping("/{id}")
    public ResponseEntity<FieldPermissionResponse> updateFieldPermission(
            @PathVariable Long id,
            @Valid @RequestBody FieldPermissionRequest request) {
        
        FieldPermissionResponse updatedPermission = fieldPermissionService.updateFieldPermission(id, request);
        return ResponseEntity.ok(updatedPermission);
    }

    /**
     * 删除字段权限
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<MessageResponse> deleteFieldPermission(@PathVariable Long id) {
        String message = fieldPermissionService.deleteFieldPermission(id);
        return ResponseEntity.ok(new MessageResponse(message));
    }

    /**
     * 批量设置字段权限
     */
    @PostMapping("/batch/{entityName}/role/{roleId}")
    public ResponseEntity<MessageResponse> batchSetFieldPermissions(
            @PathVariable String entityName,
            @PathVariable Long roleId,
            @RequestBody Map<String, Map<String, Boolean>> fieldPermissions) {
        
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String currentUsername = authentication.getName();
        
        String message = fieldPermissionService.batchSetFieldPermissions(
                entityName, fieldPermissions, roleId, currentUsername);
        
        return ResponseEntity.ok(new MessageResponse(message));
    }

    /**
     * 获取系统中所有实体及其字段信息
     */
    @GetMapping("/entity-fields-info")
    public ResponseEntity<Map<String, List<Map<String, Object>>>> getEntityFieldsInfo() {
        Map<String, List<Map<String, Object>>> entityFieldsInfo = fieldPermissionService.getEntityFieldsInfo();
        return ResponseEntity.ok(entityFieldsInfo);
    }

    /**
     * 检查用户是否有字段的读权限
     */
    @GetMapping("/check-read/{userId}/{entityName}/{fieldName}")
    public ResponseEntity<Boolean> hasReadPermission(
            @PathVariable Long userId,
            @PathVariable String entityName,
            @PathVariable String fieldName) {
        
        boolean hasPermission = fieldPermissionService.hasReadPermission(userId, entityName, fieldName);
        return ResponseEntity.ok(hasPermission);
    }

    /**
     * 检查用户是否有字段的写权限
     */
    @GetMapping("/check-write/{userId}/{entityName}/{fieldName}")
    public ResponseEntity<Boolean> hasWritePermission(
            @PathVariable Long userId,
            @PathVariable String entityName,
            @PathVariable String fieldName) {
        
        boolean hasPermission = fieldPermissionService.hasWritePermission(userId, entityName, fieldName);
        return ResponseEntity.ok(hasPermission);
    }
}