package com.example.biaozhu.controller;

import com.example.biaozhu.entity.User;
import com.example.biaozhu.payload.request.TemplateRequest;
import com.example.biaozhu.payload.response.MessageResponse;
import com.example.biaozhu.payload.response.TemplateResponse;
import com.example.biaozhu.security.CurrentUser;
import com.example.biaozhu.security.UserPrincipal;
import com.example.biaozhu.service.AnnotationTemplateService;
import com.example.biaozhu.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 标注模板控制器
 */
@RestController
@RequestMapping("/api/templates")
public class AnnotationTemplateController {

    private final AnnotationTemplateService templateService;
    private final UserService userService;

    @Autowired
    public AnnotationTemplateController(AnnotationTemplateService templateService, UserService userService) {
        this.templateService = templateService;
        this.userService = userService;
    }

    /**
     * 创建新标注模板
     */
    @PostMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<TemplateResponse> createTemplate(
            @Valid @RequestBody TemplateRequest templateRequest,
            @CurrentUser UserPrincipal currentUser) {
        
        User user = userService.getUserById(currentUser.getId());
        TemplateResponse template = templateService.createTemplate(templateRequest, user);
        
        return new ResponseEntity<>(template, HttpStatus.CREATED);
    }

    /**
     * 获取所有标注模板（分页）
     */
    @GetMapping
    public ResponseEntity<Page<TemplateResponse>> getAllTemplates(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt") String sort) {
        
        Page<TemplateResponse> templates = templateService.getAllTemplates(page, size, sort);
        
        return ResponseEntity.ok(templates);
    }

    /**
     * 根据ID获取标注模板
     */
    @GetMapping("/{id}")
    public ResponseEntity<TemplateResponse> getTemplateById(@PathVariable Long id) {
        TemplateResponse template = templateService.getTemplateById(id);
        
        return ResponseEntity.ok(template);
    }

    /**
     * 根据名称获取标注模板
     */
    @GetMapping("/name/{name}")
    public ResponseEntity<TemplateResponse> getTemplateByName(@PathVariable String name) {
        TemplateResponse template = templateService.getTemplateByName(name);
        
        return ResponseEntity.ok(template);
    }

    /**
     * 根据模板类型获取标注模板
     */
    @GetMapping("/type/{templateType}")
    public ResponseEntity<List<TemplateResponse>> getTemplatesByType(@PathVariable String templateType) {
        List<TemplateResponse> templates = templateService.getTemplatesByType(templateType);
        
        return ResponseEntity.ok(templates);
    }

    /**
     * 获取用户创建的标注模板
     */
    @GetMapping("/user/{userId}")
    public ResponseEntity<List<TemplateResponse>> getTemplatesByCreator(@PathVariable Long userId) {
        List<TemplateResponse> templates = templateService.getTemplatesByCreator(userId);
        
        return ResponseEntity.ok(templates);
    }

    /**
     * 搜索标注模板
     */
    @GetMapping("/search")
    public ResponseEntity<Page<TemplateResponse>> searchTemplates(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        Page<TemplateResponse> templates = templateService.searchTemplates(keyword, page, size);
        
        return ResponseEntity.ok(templates);
    }

    /**
     * 更新标注模板
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<TemplateResponse> updateTemplate(
            @PathVariable Long id,
            @Valid @RequestBody TemplateRequest templateRequest) {
        
        TemplateResponse updatedTemplate = templateService.updateTemplate(id, templateRequest);
        
        return ResponseEntity.ok(updatedTemplate);
    }

    /**
     * 删除标注模板
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<MessageResponse> deleteTemplate(@PathVariable Long id) {
        String message = templateService.deleteTemplate(id);
        
        return ResponseEntity.ok(new MessageResponse(message));
    }

    /**
     * 激活/停用标注模板
     */
    @PatchMapping("/{id}/status")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<TemplateResponse> toggleTemplateStatus(
            @PathVariable Long id,
            @RequestParam boolean active) {
        
        TemplateResponse template = templateService.toggleTemplateStatus(id, active);
        
        return ResponseEntity.ok(template);
    }

    /**
     * 获取模板类型统计
     */
    @GetMapping("/stats/types")
    public ResponseEntity<Map<String, Long>> getTemplateTypeStats() {
        Map<String, Long> stats = templateService.getTemplateTypeStats();
        
        return ResponseEntity.ok(stats);
    }

    /**
     * 复制现有标注模板
     */
    @PostMapping("/{id}/clone")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<TemplateResponse> cloneTemplate(
            @PathVariable Long id,
            @RequestParam String newName,
            @CurrentUser UserPrincipal currentUser) {
        
        User user = userService.getUserById(currentUser.getId());
        TemplateResponse clonedTemplate = templateService.cloneTemplate(id, newName, user);
        
        return new ResponseEntity<>(clonedTemplate, HttpStatus.CREATED);
    }

    /**
     * 获取最近创建的模板
     */
    @GetMapping("/recent")
    public ResponseEntity<List<TemplateResponse>> getRecentTemplates(
            @RequestParam(defaultValue = "5") int limit) {
        
        List<TemplateResponse> templates = templateService.getRecentTemplates(limit);
        
        return ResponseEntity.ok(templates);
    }
} 