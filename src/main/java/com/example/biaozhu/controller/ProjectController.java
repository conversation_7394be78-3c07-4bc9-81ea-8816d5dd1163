package com.example.biaozhu.controller;

import com.example.biaozhu.entity.Project;
import com.example.biaozhu.entity.User;
import com.example.biaozhu.payload.request.ProjectRequest;
import com.example.biaozhu.payload.response.MessageResponse;
import com.example.biaozhu.payload.response.ProjectResponse;
import com.example.biaozhu.repository.ProjectRepository;
import com.example.biaozhu.service.ProjectService;
import com.example.biaozhu.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 项目控制器
 * 处理项目管理相关请求
 */
@RestController
@RequestMapping("/projects")
public class ProjectController {

    private final ProjectService projectService;
    private final UserService userService;
    private final ProjectRepository projectRepository;
    
    @Autowired
    public ProjectController(ProjectService projectService, UserService userService, ProjectRepository projectRepository) {
        this.projectService = projectService;
        this.userService = userService;
        this.projectRepository = projectRepository;
    }
    
    /**
     * 创建新项目
     * 
     * @param projectRequest 项目请求对象
     * @return 创建的项目信息
     */
    @PostMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER') or hasRole('DATA_MANAGER')")
    public ResponseEntity<?> createProject(@Valid @RequestBody ProjectRequest projectRequest) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        User currentUser = userService.getUserByUsername(authentication.getName());
        
        Project project = projectService.createProject(projectRequest, currentUser);
        return new ResponseEntity<>(new ProjectResponse(project), HttpStatus.CREATED);
    }
    
    /**
     * 获取所有项目（分页）
     * 
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序字段
     * @return 项目分页数据
     */
    @GetMapping
    public ResponseEntity<?> getAllProjects(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sort) {
        
        Page<Project> projectsPage = projectService.getAllProjects(page, size, sort);
        
        Map<String, Object> response = new HashMap<>();
        response.put("projects", projectsPage.getContent().stream()
                .map(ProjectResponse::new)
                .collect(Collectors.toList()));
        response.put("currentPage", projectsPage.getNumber());
        response.put("totalItems", projectsPage.getTotalElements());
        response.put("totalPages", projectsPage.getTotalPages());
        
        return new ResponseEntity<>(response, HttpStatus.OK);
    }
    
    /**
     * 获取当前用户的项目
     * 
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序字段
     * @return 项目分页数据
     */
    @GetMapping("/me")
    public ResponseEntity<?> getMyProjects(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sort) {
        
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        User currentUser = userService.getUserByUsername(authentication.getName());
        
        Page<Project> projectsPage = projectService.getProjectsByUser(currentUser, page, size, sort);
        
        Map<String, Object> response = new HashMap<>();
        response.put("projects", projectsPage.getContent().stream()
                .map(ProjectResponse::new)
                .collect(Collectors.toList()));
        response.put("currentPage", projectsPage.getNumber());
        response.put("totalItems", projectsPage.getTotalElements());
        response.put("totalPages", projectsPage.getTotalPages());
        
        return new ResponseEntity<>(response, HttpStatus.OK);
    }
    
    /**
     * 根据ID获取项目
     * 
     * @param id 项目ID
     * @return 项目信息响应
     */
    @GetMapping("/{id}")
    public ResponseEntity<?> getProjectById(@PathVariable Long id) {
        Project project = projectService.getProjectById(id);
        return ResponseEntity.ok(new ProjectResponse(project));
    }
    
    /**
     * 更新项目信息
     * 
     * @param id 项目ID
     * @param projectRequest 项目请求对象
     * @return 更新后的项目信息
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER') or hasRole('DATA_MANAGER') or @projectService.isProjectCreator(#id)")
    public ResponseEntity<?> updateProject(
            @PathVariable Long id, 
            @Valid @RequestBody ProjectRequest projectRequest) {
        
        Project updatedProject = projectService.updateProject(id, projectRequest);
        return ResponseEntity.ok(new ProjectResponse(updatedProject));
    }
    
    /**
     * 删除项目
     * 
     * @param id 项目ID
     * @return 删除成功响应
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER') or hasRole('DATA_MANAGER') or @projectService.isProjectCreator(#id)")
    public ResponseEntity<?> deleteProject(@PathVariable Long id) {
        projectService.deleteProject(id);
        return ResponseEntity.ok(new MessageResponse("项目删除成功"));
    }
    
    /**
     * 根据关键词搜索项目
     * 
     * @param keyword 关键词
     * @param page 页码
     * @param size 每页大小
     * @return 项目搜索结果
     */
    @GetMapping("/search")
    public ResponseEntity<?> searchProjects(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        Page<Project> projectsPage = projectService.searchProjects(keyword, page, size);
        
        Map<String, Object> response = new HashMap<>();
        response.put("projects", projectsPage.getContent().stream()
                .map(ProjectResponse::new)
                .collect(Collectors.toList()));
        response.put("currentPage", projectsPage.getNumber());
        response.put("totalItems", projectsPage.getTotalElements());
        response.put("totalPages", projectsPage.getTotalPages());
        
        return new ResponseEntity<>(response, HttpStatus.OK);
    }
    
    /**
     * 获取项目统计信息
     * 
     * @param id 项目ID
     * @return 项目统计信息
     */
    @GetMapping("/{id}/statistics")
    public ResponseEntity<?> getProjectStatistics(@PathVariable Long id) {
        Map<String, Object> statistics = projectService.getProjectStatistics(id);
        return ResponseEntity.ok(statistics);
    }
    
    /**
     * 获取所有项目状态统计
     * 
     * @return 项目状态统计信息
     */
    @GetMapping("/statistics/status")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER') or hasRole('DATA_MANAGER')")
    public ResponseEntity<?> getProjectStatusStatistics() {
        List<Map<String, Object>> statistics = projectService.getProjectStatusStatistics();
        return ResponseEntity.ok(statistics);
    }

    /**
     * 获取项目的数据集列表
     * 
     * @param id 项目ID
     * @return 数据集列表
     */
    @GetMapping("/{id}/datasets")
    public ResponseEntity<?> getProjectDatasets(@PathVariable Long id) {
        Project project = projectService.getProjectById(id);
        return ResponseEntity.ok(project.getDatasets().stream()
                .map(dataset -> {
                    Map<String, Object> datasetInfo = new HashMap<>();
                    datasetInfo.put("id", dataset.getId());
                    datasetInfo.put("name", dataset.getName());
                    datasetInfo.put("dataType", dataset.getType());
                    datasetInfo.put("itemCount", dataset.getDataItems() != null ? dataset.getDataItems().size() : 0);
                    datasetInfo.put("createdAt", dataset.getCreatedAt());
                    return datasetInfo;
                })
                .collect(Collectors.toList()));
    }

    /**
     * 获取项目的任务列表
     * 
     * @param id 项目ID
     * @return 任务列表
     */
    @GetMapping("/{id}/tasks")
    public ResponseEntity<?> getProjectTasks(@PathVariable Long id) {
        Project project = projectService.getProjectById(id);
        return ResponseEntity.ok(project.getTasks().stream()
                .map(task -> {
                    Map<String, Object> taskInfo = new HashMap<>();
                    taskInfo.put("id", task.getId());
                    taskInfo.put("name", task.getName());
                    taskInfo.put("status", task.getStatus());
                    taskInfo.put("progress", task.getProgress());
                    
                    // 使用 getAssignedTo 替代 getAssignee
                    if (task.getAssignedTo() != null) {
                        Map<String, Object> assigneeInfo = new HashMap<>();
                        assigneeInfo.put("id", task.getAssignedTo().getId());
                        assigneeInfo.put("name", task.getAssignedTo().getUsername());
                        taskInfo.put("assignee", assigneeInfo);
                    } else {
                        taskInfo.put("assignee", null);
                    }
                    
                    taskInfo.put("datasetId", task.getDataset() != null ? task.getDataset().getId() : null);
                    return taskInfo;
                })
                .collect(Collectors.toList()));
    }

    /**
     * 获取项目的成员列表
     * 
     * @param id 项目ID
     * @return 成员列表
     */
    @GetMapping("/{id}/members")
    public ResponseEntity<?> getProjectMembers(@PathVariable Long id) {
        Project project = projectService.getProjectById(id);
        
        List<Map<String, Object>> membersList = new ArrayList<>();
        
        // 添加项目创建者
        if (project.getCreatedBy() != null) {
            Map<String, Object> creatorInfo = new HashMap<>();
            creatorInfo.put("id", -1L); // 使用特殊ID表示创建者
            creatorInfo.put("userId", project.getCreatedBy().getId());
            creatorInfo.put("name", project.getCreatedBy().getUsername());
            creatorInfo.put("email", project.getCreatedBy().getEmail());
            creatorInfo.put("role", "ADMIN");
            // 使用固定日期替代
            creatorInfo.put("joinedAt", new java.util.Date());
            membersList.add(creatorInfo);
        }
        
        // 添加项目管理者
        for (User manager : project.getManagers()) {
            Map<String, Object> managerInfo = new HashMap<>();
            managerInfo.put("id", manager.getId());
            managerInfo.put("userId", manager.getId());
            managerInfo.put("name", manager.getUsername());
            managerInfo.put("email", manager.getEmail());
            managerInfo.put("role", "MANAGER");
            // 使用固定日期替代
            managerInfo.put("joinedAt", new java.util.Date());
            membersList.add(managerInfo);
        }
        
        // 添加普通项目成员
        for (User member : project.getMembers()) {
            Map<String, Object> memberInfo = new HashMap<>();
            memberInfo.put("id", member.getId());
            memberInfo.put("userId", member.getId());
            memberInfo.put("name", member.getUsername());
            memberInfo.put("email", member.getEmail());
            memberInfo.put("role", "ANNOTATOR");
            // 使用固定日期替代
            memberInfo.put("joinedAt", new java.util.Date());
            membersList.add(memberInfo);
        }
        
        return ResponseEntity.ok(membersList);
    }

    /**
     * 归档项目
     * 
     * @param id 项目ID
     * @return 成功响应
     */
    @PostMapping("/{id}/archive")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER') or hasRole('DATA_MANAGER') or @projectService.isProjectCreator(#id)")
    public ResponseEntity<?> archiveProject(@PathVariable Long id) {
        Project project = projectService.archiveProject(id);
        return ResponseEntity.ok(new MessageResponse("项目已归档"));
    }

    /**
     * 恢复已归档项目
     * 
     * @param id 项目ID
     * @return 成功响应
     */
    @PostMapping("/{id}/restore")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER') or hasRole('DATA_MANAGER') or @projectService.isProjectCreator(#id)")
    public ResponseEntity<?> restoreProject(@PathVariable Long id) {
        Project project = projectService.restoreProject(id);
        return ResponseEntity.ok(new MessageResponse("项目已恢复"));
    }

    /**
     * 添加项目成员
     * 
     * @param id 项目ID
     * @param memberRequest 成员请求对象
     * @return 成功响应
     */
    @PostMapping("/{id}/members")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER') or hasRole('DATA_MANAGER') or @projectService.isProjectCreator(#id)")
    public ResponseEntity<?> addProjectMember(
            @PathVariable Long id,
            @Valid @RequestBody Map<String, Object> memberRequest) {
        
        Long userId = Long.parseLong(memberRequest.get("userId").toString());
        String role = memberRequest.get("role").toString();
        
        Project project = projectService.getProjectById(id);
        User user = userService.getUserById(userId);
        
        if ("MANAGER".equals(role)) {
            project.getManagers().add(user);
        } else {
            project.getMembers().add(user);
        }
        
        projectRepository.save(project);
        
        return ResponseEntity.ok(new MessageResponse("成员已添加到项目"));
    }

    /**
     * 更新项目成员角色
     * 
     * @param id 项目ID
     * @param memberId 成员ID
     * @param roleRequest 角色请求对象
     * @return 成功响应
     */
    @PutMapping("/{id}/members/{memberId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER') or hasRole('DATA_MANAGER') or @projectService.isProjectCreator(#id)")
    public ResponseEntity<?> updateProjectMemberRole(
            @PathVariable Long id,
            @PathVariable Long memberId,
            @Valid @RequestBody Map<String, Object> roleRequest) {
        
        String role = roleRequest.get("role").toString();
        
        Project project = projectService.getProjectById(id);
        User user = userService.getUserById(memberId);
        
        // 从所有角色中移除用户
        project.getManagers().remove(user);
        project.getMembers().remove(user);
        
        // 添加到新角色
        if ("MANAGER".equals(role)) {
            project.getManagers().add(user);
        } else if ("ANNOTATOR".equals(role) || "REVIEWER".equals(role) || "VIEWER".equals(role)) {
            project.getMembers().add(user);
        }
        
        projectRepository.save(project);
        
        return ResponseEntity.ok(new MessageResponse("成员角色已更新"));
    }

    /**
     * 移除项目成员
     * 
     * @param id 项目ID
     * @param memberId 成员ID
     * @return 成功响应
     */
    @DeleteMapping("/{id}/members/{memberId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER') or hasRole('DATA_MANAGER') or @projectService.isProjectCreator(#id)")
    public ResponseEntity<?> removeProjectMember(
            @PathVariable Long id,
            @PathVariable Long memberId) {
        
        Project project = projectService.getProjectById(id);
        User user = userService.getUserById(memberId);
        
        project.getManagers().remove(user);
        project.getMembers().remove(user);
        
        projectRepository.save(project);
        
        return ResponseEntity.ok(new MessageResponse("成员已从项目中移除"));
    }
} 