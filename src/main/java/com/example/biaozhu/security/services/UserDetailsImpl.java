package com.example.biaozhu.security.services;

import com.example.biaozhu.entity.User;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * UserDetails实现类
 * 封装安全用户详细信息
 */
public class UserDetailsImpl implements UserDetails {
    private static final long serialVersionUID = 1L;

    private Long id;
    private String username;
    private String email;
    private String fullName;
    
    @JsonIgnore
    private String password;
    
    private Collection<? extends GrantedAuthority> authorities;

    public UserDetailsImpl(Long id, String username, String email, String password,
                          Collection<? extends GrantedAuthority> authorities) {
        this.id = id;
        this.username = username;
        this.email = email;
        this.password = password;
        this.authorities = authorities;
    }
    
    public UserDetailsImpl(Long id, String username, String email, String fullName, String password,
                          Collection<? extends GrantedAuthority> authorities) {
        this.id = id;
        this.username = username;
        this.email = email;
        this.fullName = fullName;
        this.password = password;
        this.authorities = authorities;
    }

    /**
     * 从用户实体构建UserDetails对象
     * 
     * @param user 用户实体
     * @return UserDetailsImpl对象
     */
    public static UserDetailsImpl build(User user) {
        List<GrantedAuthority> authorities = new ArrayList<>();
        
        // 处理用户角色信息
        if (user.getRoles() != null) {
            authorities = user.getRoles().stream()
                .map(role -> {
                    try {
                        // 尝试使用角色名作为权限标识符
                        String roleName = role.getName().name();
                        return new SimpleGrantedAuthority("ROLE_" + roleName);
                    } catch (Exception e) {
                        // 如果获取角色名失败，记录错误并使用固定格式
                        System.err.println("获取角色名失败: " + e.getMessage());
                        return new SimpleGrantedAuthority("ROLE_" + role.getId());
                    }
                })
                .collect(Collectors.toList());
            
            // 确保有至少一个默认权限
            if (authorities.isEmpty()) {
                authorities.add(new SimpleGrantedAuthority("ROLE_USER"));
            }
        } else {
            // 没有角色时，添加默认权限
            authorities.add(new SimpleGrantedAuthority("ROLE_USER"));
        }

        return new UserDetailsImpl(
                user.getId(),
                user.getUsername(),
                user.getEmail(),
                user.getFullName(),
                user.getPassword(),
                authorities);
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return authorities;
    }

    public Long getId() {
        return id;
    }

    public String getEmail() {
        return email;
    }
    
    /**
     * 获取用户全名
     * @return 用户全名
     */
    public String getName() {
        return fullName != null ? fullName : username;
    }
    
    /**
     * 获取用户全名
     * @return 用户全名
     */
    public String getFullName() {
        return fullName;
    }

    @Override
    public String getPassword() {
        return password;
    }

    @Override
    public String getUsername() {
        return username;
    }

    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    public boolean isEnabled() {
        return true;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;
        UserDetailsImpl user = (UserDetailsImpl) o;
        return Objects.equals(id, user.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
} 