package com.example.biaozhu.repository;

import com.example.biaozhu.entity.DataItem;
import com.example.biaozhu.entity.DataItem.DataType;
import com.example.biaozhu.entity.DataItem.SplitType;
import com.example.biaozhu.entity.Dataset;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 数据项仓库接口
 */
@Repository
public interface DataItemRepository extends JpaRepository<DataItem, Long> {
    
    /**
     * 根据数据集查找数据项
     * @param dataset 数据集
     * @return 数据项列表
     */
    List<DataItem> findByDataset(Dataset dataset);
    
    /**
     * 根据数据类型查找数据项
     * @param type 数据类型
     * @return 数据项列表
     */
    List<DataItem> findByType(String type);
    
    /**
     * 根据是否已标注查找数据项
     * @param annotated 是否已标注
     * @return 数据项列表
     */
    List<DataItem> findByAnnotated(boolean annotated);
    
    /**
     * 根据分割类型查找数据项
     * @param splitType 分割类型
     * @return 数据项列表
     */
    List<DataItem> findBySplitType(String splitType);
    
    /**
     * 查找指定数据集中未标注的数据项
     * @param dataset 数据集
     * @param annotated 是否已标注
     * @return 数据项列表
     */
    List<DataItem> findByDatasetAndAnnotated(Dataset dataset, boolean annotated);
    
    /**
     * 查找指定数据集和类型的数据项
     * @param dataset 数据集
     * @param type 数据类型
     * @return 数据项列表
     */
    List<DataItem> findByDatasetAndType(Dataset dataset, String type);
    
    /**
     * 查找指定数据集和分割类型的数据项
     * @param dataset 数据集
     * @param splitType 分割类型
     * @return 数据项列表
     */
    List<DataItem> findByDatasetAndSplitType(Dataset dataset, String splitType);
    
    /**
     * 根据创建时间范围查找数据项
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 数据项列表
     */
    List<DataItem> findByCreatedAtBetween(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 根据文件大小范围查找数据项
     * @param minSize 最小文件大小
     * @param maxSize 最大文件大小
     * @return 数据项列表
     */
    List<DataItem> findByFileSizeBetween(Long minSize, Long maxSize);
    
    /**
     * 根据MIME类型查找数据项
     * @param fileType MIME类型
     * @return 数据项列表
     */
    List<DataItem> findByFileType(String fileType);
    
    /**
     * 根据关键词搜索数据项（名称和文本内容）
     * @param keyword 关键词
     * @param pageable 分页参数
     * @return 分页数据项结果
     */
    @Query("SELECT d FROM DataItem d WHERE d.name LIKE CONCAT('%', :keyword, '%') OR d.content LIKE CONCAT('%', :keyword, '%')")
    Page<DataItem> searchByKeyword(@Param("keyword") String keyword, Pageable pageable);
    
    /**
     * 根据标识符查找数据项
     * @param identifier 标识符
     * @return 数据项列表
     */
    List<DataItem> findByIdentifier(String identifier);
    
    /**
     * 统计数据集的数据项数量
     * @param dataset 数据集
     * @return 数据项数量
     */
    long countByDataset(Dataset dataset);
    
    /**
     * 统计各类型数据项的数量
     * @return 类型和数量的对应关系
     */
    @Query("SELECT d.type, COUNT(d) FROM DataItem d GROUP BY d.type")
    List<Object[]> countByType();
    
    /**
     * 统计各分割类型数据项的数量
     * @return 分割类型和数量的对应关系
     */
    @Query("SELECT d.splitType, COUNT(d) FROM DataItem d GROUP BY d.splitType")
    List<Object[]> countBySplitType();
    
    /**
     * 按数据集查询数据项（分页）
     * 
     * @param dataset 数据集
     * @param pageable 分页参数
     * @return 数据项分页结果
     */
    Page<DataItem> findByDataset(Dataset dataset, Pageable pageable);
    
    /**
     * 按数据集ID查询数据项（分页）
     * 
     * @param datasetId 数据集ID
     * @param pageable 分页参数
     * @return 数据项分页结果
     */
    Page<DataItem> findByDatasetId(Long datasetId, Pageable pageable);
    
    /**
     * 按数据集ID和类型查询数据项（分页）
     * 
     * @param datasetId 数据集ID
     * @param type 数据项类型
     * @param pageable 分页参数
     * @return 数据项分页结果
     */
    Page<DataItem> findByDatasetIdAndType(Long datasetId, String type, Pageable pageable);
    
    /**
     * 按数据集和名称查询数据项
     * 
     * @param dataset 数据集
     * @param name 数据项名称
     * @return 数据项列表
     */
    List<DataItem> findByDatasetAndName(Dataset dataset, String name);
    
    /**
     * 按数据集ID和名称模糊查询数据项（分页）
     * 
     * @param datasetId 数据集ID
     * @param name 数据项名称关键字
     * @param pageable 分页参数
     * @return 数据项分页结果
     */
    Page<DataItem> findByDatasetIdAndNameContaining(Long datasetId, String name, Pageable pageable);
    
    /**
     * 按创建者ID查询数据项（分页）
     * 
     * @param creatorId 创建者ID
     * @param pageable 分页参数
     * @return 数据项分页结果
     */
    Page<DataItem> findByCreatorId(Long creatorId, Pageable pageable);
    
    /**
     * 获取数据集中数据项的数量
     * 
     * @param datasetId 数据集ID
     * @return 数据项数量
     */
    long countByDatasetId(Long datasetId);
    
    /**
     * 按类型统计数据集中的数据项数量
     * 
     * @param datasetId 数据集ID
     * @return 各类型数据项数量
     */
    @Query("SELECT di.type, COUNT(di) FROM DataItem di WHERE di.dataset.id = :datasetId GROUP BY di.type")
    List<Object[]> countByDatasetIdGroupByType(@Param("datasetId") Long datasetId);
    
    /**
     * 查询指定任务使用的数据项
     * 
     * @param taskId 任务ID
     * @return 数据项列表
     */
    @Query("SELECT DISTINCT di FROM DataItem di JOIN Task t ON di.dataset = t.dataset WHERE t.id = :taskId")
    List<DataItem> findByTaskId(@Param("taskId") Long taskId);
    
    /**
     * 查询指定任务使用的数据项（分页）
     * 
     * @param taskId 任务ID
     * @param pageable 分页参数
     * @return 数据项分页结果
     */
    @Query("SELECT DISTINCT di FROM DataItem di JOIN Task t ON di.dataset = t.dataset WHERE t.id = :taskId")
    Page<DataItem> findByTaskId(@Param("taskId") Long taskId, Pageable pageable);
    
    /**
     * 查询已标注的数据项
     * 
     * @param datasetId 数据集ID
     * @return 数据项列表
     */
    @Query("SELECT DISTINCT di FROM DataItem di JOIN di.annotations a WHERE di.dataset.id = :datasetId")
    List<DataItem> findAnnotatedItemsByDatasetId(@Param("datasetId") Long datasetId);
    
    /**
     * 查询未标注的数据项
     * 
     * @param datasetId 数据集ID
     * @return 数据项列表
     */
    @Query("SELECT di FROM DataItem di WHERE di.dataset.id = :datasetId AND NOT EXISTS (SELECT a FROM Annotation a WHERE a.dataItem = di)")
    List<DataItem> findUnannotatedItemsByDatasetId(@Param("datasetId") Long datasetId);
    
    /**
     * 查询未标注的数据项（分页）
     * 
     * @param datasetId 数据集ID
     * @param pageable 分页参数
     * @return 数据项分页结果
     */
    @Query("SELECT di FROM DataItem di WHERE di.dataset.id = :datasetId AND NOT EXISTS (SELECT a FROM Annotation a WHERE a.dataItem = di)")
    Page<DataItem> findUnannotatedItemsByDatasetId(@Param("datasetId") Long datasetId, Pageable pageable);
    
    /**
     * 查询已标注数据项的数量
     * 
     * @param datasetId 数据集ID
     * @return 已标注数据项数量
     */
    @Query("SELECT COUNT(DISTINCT di) FROM DataItem di JOIN di.annotations a WHERE di.dataset.id = :datasetId")
    long countAnnotatedItemsByDatasetId(@Param("datasetId") Long datasetId);
    
    /**
     * 查询未标注数据项的数量
     * 
     * @param datasetId 数据集ID
     * @return 未标注数据项数量
     */
    @Query("SELECT COUNT(di) FROM DataItem di WHERE di.dataset.id = :datasetId AND NOT EXISTS (SELECT a FROM Annotation a WHERE a.dataItem = di)")
    long countUnannotatedItemsByDatasetId(@Param("datasetId") Long datasetId);
} 