package com.example.biaozhu.repository;

import com.example.biaozhu.entity.Permission;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * 权限仓库接口
 */
@Repository
public interface PermissionRepository extends JpaRepository<Permission, Long> {

    /**
     * 根据权限名查找权限
     * 
     * @param name 权限名
     * @return 权限对象（Optional包装）
     */
    Optional<Permission> findByName(String name);
    
    /**
     * 根据权限名集合查找权限
     * 
     * @param names 权限名集合
     * @return 权限集合
     */
    Set<Permission> findByNameIn(Set<String> names);
    
    /**
     * 检查权限名是否存在
     * 
     * @param name 权限名
     * @return 是否存在
     */
    boolean existsByName(String name);
    
    /**
     * 根据类别查找权限
     * 
     * @param category 类别
     * @return 权限列表
     */
    List<Permission> findByCategory(String category);
    
    /**
     * 根据角色ID查找权限
     * 
     * @param roleId 角色ID
     * @return 权限集合
     */
    List<Permission> findByRolesId(Long roleId);
} 