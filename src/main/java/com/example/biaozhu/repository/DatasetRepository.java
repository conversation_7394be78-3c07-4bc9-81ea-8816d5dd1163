package com.example.biaozhu.repository;

import com.example.biaozhu.entity.Dataset;
import com.example.biaozhu.entity.Dataset.DatasetStatus;
import com.example.biaozhu.entity.Dataset.DatasetType;
import com.example.biaozhu.entity.Dataset.Visibility;
import com.example.biaozhu.entity.Project;
import com.example.biaozhu.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 数据集仓库接口
 */
@Repository
public interface DatasetRepository extends JpaRepository<Dataset, Long> {
    
    /**
     * 根据项目查找数据集
     * 
     * @param project 项目
     * @return 数据集列表
     */
    List<Dataset> findByProject(Project project);
    
    /**
     * 根据创建者查找数据集
     * 
     * @param creator 创建者
     * @return 数据集列表
     */
    List<Dataset> findByCreator(User creator);
    
    /**
     * 根据数据类型查找数据集
     * 
     * @param type 数据类型
     * @return 数据集列表
     */
    List<Dataset> findByType(String type);
    
    /**
     * 根据公开状态查找数据集
     * 
     * @param isPublic 公开状态
     * @return 数据集列表
     */
    List<Dataset> findByIsPublic(Boolean isPublic);
    
    /**
     * 根据名称模糊查询数据集
     * 
     * @param name 数据集名称
     * @return 数据集列表
     */
    List<Dataset> findByNameContaining(String name);
    
    /**
     * 根据项目和数据类型查找数据集
     * 
     * @param project 项目
     * @param type 数据类型
     * @return 数据集列表
     */
    List<Dataset> findByProjectAndType(Project project, String type);
    
    /**
     * 根据类型查找数据集
     * @param type 数据集类型
     * @return 数据集列表
     */
    List<Dataset> findByType(DatasetType type);
    
    /**
     * 根据可见性查找数据集
     * @param visibility 可见性
     * @return 数据集列表
     */
    List<Dataset> findByVisibility(Visibility visibility);
    
    /**
     * 根据状态查找数据集
     * @param status 数据集状态
     * @return 数据集列表
     */
    List<Dataset> findByStatus(DatasetStatus status);
    
    /**
     * 根据关键词搜索数据集（名称和描述）
     * @param keyword 关键词
     * @param pageable 分页参数
     * @return 分页数据集结果
     */
    @Query("SELECT d FROM Dataset d WHERE d.name LIKE %:keyword% OR d.description LIKE %:keyword%")
    Page<Dataset> searchByKeyword(String keyword, Pageable pageable);
    
    /**
     * 根据标签查找数据集
     * @param label 标签
     * @return 数据集列表
     */
    @Query("SELECT d FROM Dataset d JOIN d.labels l WHERE l = :label")
    List<Dataset> findByLabel(@Param("label") String label);
    
    /**
     * 查找项目公开数据集
     * @param project 项目
     * @return 数据集列表
     */
    List<Dataset> findByProjectAndVisibility(Project project, Visibility visibility);
    
    /**
     * 查找最大数据项数量的前N个数据集
     * @param limit 限制数量
     * @return 数据集列表
     */
    @Query("SELECT d FROM Dataset d ORDER BY d.itemCount DESC")
    List<Dataset> findTopByItemCount(Pageable pageable);
    
    /**
     * 统计各类型数据集数量
     * @return 类型和数量的对应关系
     */
    @Query("SELECT d.type, COUNT(d) FROM Dataset d GROUP BY d.type")
    List<Object[]> countByType();

    /**
     * 按名称查找数据集
     * 
     * @param name 数据集名称
     * @return 数据集对象（可选）
     */
    Optional<Dataset> findByName(String name);
    
    /**
     * 按名称模糊查询数据集（分页）
     * 
     * @param name 数据集名称关键字
     * @param pageable 分页参数
     * @return 数据集分页结果
     */
    Page<Dataset> findByNameContaining(String name, Pageable pageable);
    
    /**
     * 按类型查询数据集（分页）
     * 
     * @param type 数据集类型
     * @param pageable 分页参数
     * @return 数据集分页结果
     */
    Page<Dataset> findByType(String type, Pageable pageable);
    
    /**
     * 查询公开数据集（分页）
     * 
     * @param pageable 分页参数
     * @return 数据集分页结果
     */
    Page<Dataset> findByIsPublicTrue(Pageable pageable);
    
    /**
     * 按创建者查询数据集（分页）
     * 
     * @param creator 创建者
     * @param pageable 分页参数
     * @return 数据集分页结果
     */
    Page<Dataset> findByCreator(User creator, Pageable pageable);
    
    /**
     * 按创建者ID查询数据集（分页）
     * 
     * @param creatorId 创建者ID
     * @param pageable 分页参数
     * @return 数据集分页结果
     */
    Page<Dataset> findByCreatorId(Long creatorId, Pageable pageable);
    
    /**
     * 查询用户有权访问的数据集（分页）
     * 
     * @param user 用户
     * @param pageable 分页参数
     * @return 数据集分页结果
     */
    @Query("SELECT d FROM Dataset d WHERE d.isPublic = true OR d.creator = :user OR :user MEMBER OF d.authorizedUsers")
    Page<Dataset> findAccessibleDatasets(@Param("user") User user, Pageable pageable);
    
    /**
     * 查询用户有权访问的数据集数量
     * 
     * @param user 用户
     * @return 数据集数量
     */
    @Query("SELECT COUNT(d) FROM Dataset d WHERE d.isPublic = true OR d.creator = :user OR :user MEMBER OF d.authorizedUsers")
    long countAccessibleDatasets(@Param("user") User user);
    
    /**
     * 按类型查询用户有权访问的数据集（分页）
     * 
     * @param user 用户
     * @param type 数据集类型
     * @param pageable 分页参数
     * @return 数据集分页结果
     */
    @Query("SELECT d FROM Dataset d WHERE (d.isPublic = true OR d.creator = :user OR :user MEMBER OF d.authorizedUsers) AND d.type = :type")
    Page<Dataset> findAccessibleDatasetsByType(@Param("user") User user, @Param("type") String type, Pageable pageable);
    
    /**
     * 按名称模糊查询用户有权访问的数据集（分页）
     * 
     * @param user 用户
     * @param name 数据集名称关键字
     * @param pageable 分页参数
     * @return 数据集分页结果
     */
    @Query("SELECT d FROM Dataset d WHERE (d.isPublic = true OR d.creator = :user OR :user MEMBER OF d.authorizedUsers) AND d.name LIKE %:name%")
    Page<Dataset> findAccessibleDatasetsByName(@Param("user") User user, @Param("name") String name, Pageable pageable);
    
    /**
     * 检查用户是否有权访问数据集
     * 
     * @param datasetId 数据集ID
     * @param userId 用户ID
     * @return 是否有权访问
     */
    @Query("SELECT CASE WHEN COUNT(d) > 0 THEN true ELSE false END FROM Dataset d WHERE d.id = :datasetId AND (d.isPublic = true OR d.creator.id = :userId OR EXISTS (SELECT u FROM d.authorizedUsers u WHERE u.id = :userId))")
    boolean hasAccessToDataset(@Param("datasetId") Long datasetId, @Param("userId") Long userId);
    
    /**
     * 查询任务相关的所有数据集
     * 
     * @param taskId 任务ID
     * @return 数据集列表
     */
    @Query("SELECT d FROM Dataset d JOIN d.tasks t WHERE t.id = :taskId")
    List<Dataset> findByTaskId(@Param("taskId") Long taskId);
    
    /**
     * 根据授权用户查找数据集（分页）
     * 
     * @param user 授权用户
     * @param pageable 分页参数
     * @return 分页数据集结果
     */
    @Query("SELECT d FROM Dataset d WHERE :user MEMBER OF d.authorizedUsers")
    Page<Dataset> findByAuthorizedUsersContaining(@Param("user") User user, Pageable pageable);
    
    /**
     * 根据项目ID查找数据集
     * 
     * @param projectId 项目ID
     * @return 数据集列表
     */
    @Query("SELECT d FROM Dataset d WHERE d.project.id = :projectId")
    List<Dataset> findByProjectId(@Param("projectId") Long projectId);
    
    /**
     * 根据项目ID分页查找数据集
     * 
     * @param projectId 项目ID
     * @param pageable 分页参数
     * @return 数据集分页结果
     */
    @Query("SELECT d FROM Dataset d WHERE d.project.id = :projectId")
    Page<Dataset> findByProjectId(@Param("projectId") Long projectId, Pageable pageable);
} 