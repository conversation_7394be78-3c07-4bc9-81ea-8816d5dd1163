package com.example.biaozhu.repository;

import com.example.biaozhu.entity.Team;
import com.example.biaozhu.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 团队数据访问接口
 */
@Repository
public interface TeamRepository extends JpaRepository<Team, Long> {
    
    /**
     * 根据团队名称查找团队
     * 
     * @param name 团队名称
     * @return 团队对象
     */
    Optional<Team> findByName(String name);
    
    /**
     * 检查团队名称是否已存在
     * 
     * @param name 团队名称
     * @return 是否存在
     */
    boolean existsByName(String name);
    
    /**
     * 查找用户创建的团队
     * 
     * @param creator 创建者
     * @param pageable 分页参数
     * @return 团队分页对象
     */
    Page<Team> findByCreator(User creator, Pageable pageable);
    
    /**
     * 查找用户所属的团队
     * 
     * @param member 成员
     * @param pageable 分页参数
     * @return 团队分页对象
     */
    @Query("SELECT t FROM Team t JOIN t.members m WHERE m = :member")
    Page<Team> findByMember(@Param("member") User member, Pageable pageable);
    
    /**
     * 查找用户管理的团队
     * 
     * @param admin 管理员
     * @param pageable 分页参数
     * @return 团队分页对象
     */
    @Query("SELECT t FROM Team t JOIN t.admins a WHERE a = :admin")
    Page<Team> findByAdmin(@Param("admin") User admin, Pageable pageable);
    
    /**
     * 按名称搜索团队
     * 
     * @param keyword 关键词
     * @param pageable 分页参数
     * @return 团队分页对象
     */
    @Query("SELECT t FROM Team t WHERE t.name LIKE %:keyword% OR t.description LIKE %:keyword%")
    Page<Team> searchByKeyword(@Param("keyword") String keyword, Pageable pageable);
    
    /**
     * 查找包含特定项目的团队
     * 
     * @param projectId 项目ID
     * @return 团队列表
     */
    @Query("SELECT t FROM Team t JOIN t.projects p WHERE p.id = :projectId")
    List<Team> findByProjectId(@Param("projectId") Long projectId);
    
    /**
     * 获取按状态分组的团队统计
     * 
     * @return 团队状态统计结果
     */
    @Query("SELECT t.status as status, COUNT(t) as count FROM Team t GROUP BY t.status")
    List<Object[]> countByStatus();
    
    /**
     * 查询具有最多成员的团队
     * 
     * @param limit 限制结果数
     * @return 团队列表
     */
    @Query(value = "SELECT t.* FROM teams t " +
                  "LEFT JOIN (SELECT team_id, COUNT(*) as member_count FROM team_members GROUP BY team_id) tm " +
                  "ON t.id = tm.team_id " +
                  "ORDER BY tm.member_count DESC LIMIT :limit", 
                  nativeQuery = true)
    List<Team> findTopTeamsByMemberCount(@Param("limit") int limit);
} 