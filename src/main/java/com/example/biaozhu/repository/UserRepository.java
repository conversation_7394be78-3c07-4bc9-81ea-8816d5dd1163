package com.example.biaozhu.repository;

import com.example.biaozhu.entity.Role;
import com.example.biaozhu.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 用户仓库接口
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    
    /**
     * 根据用户名查找用户
     * 
     * @param username 用户名
     * @return 用户对象（Optional包装）
     */
    Optional<User> findByUsername(String username);
    
    /**
     * 根据邮箱查找用户
     * 
     * @param email 邮箱
     * @return 用户对象（Optional包装）
     */
    Optional<User> findByEmail(String email);
    
    /**
     * 检查用户名是否存在
     * 
     * @param username 用户名
     * @return 是否存在
     */
    Boolean existsByUsername(String username);
    
    /**
     * 检查邮箱是否存在
     * 
     * @param email 邮箱
     * @return 是否存在
     */
    Boolean existsByEmail(String email);
    
    /**
     * 查找活跃用户
     * 
     * @return 活跃用户列表
     */
    List<User> findByActiveTrue();
    
    /**
     * 根据用户名或邮箱查找用户
     * 
     * @param username 用户名
     * @param email 邮箱
     * @return 用户对象（Optional包装）
     */
    Optional<User> findByUsernameOrEmail(String username, String email);
    
    /**
     * 根据角色名查找用户
     * @param roleName 角色名
     * @return 用户列表
     */
    @Query("SELECT u FROM User u JOIN u.roles r WHERE r.name = :roleName")
    List<User> findByRoleName(String roleName);
    
    /**
     * 查找标注任务最多的用户（前N个）
     * @param limit 数量限制
     * @return 用户列表
     */
    @Query(value = "SELECT * FROM users ORDER BY task_completed_count DESC LIMIT :limit", nativeQuery = true)
    List<User> findTopAnnotators(int limit);
    
    /**
     * 根据准确率查找用户（大于等于指定值）
     * @param minAccuracy 最小准确率
     * @return 用户列表
     */
    List<User> findByAccuracyRateGreaterThanEqual(double minAccuracy);
    
    /**
     * 统计拥有指定角色ID的用户数量
     * @param roleId 角色ID
     * @return 用户数量
     */
    @Query("SELECT COUNT(u) FROM User u JOIN u.roles r WHERE r.id = :roleId")
    Long countByRolesId(Long roleId);
    
    /**
     * 根据角色查找用户
     * @param role 角色
     * @return 用户列表
     */
    @Query("SELECT u FROM User u JOIN u.roles r WHERE r = :role")
    List<User> findByRoles(Role role);
} 