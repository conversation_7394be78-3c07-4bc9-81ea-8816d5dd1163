package com.example.biaozhu.repository;

import com.example.biaozhu.entity.Role;
import com.example.biaozhu.entity.Role.ERole;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 角色仓库接口
 */
@Repository
public interface RoleRepository extends JpaRepository<Role, Long> {
    
    /**
     * 根据角色名查找角色
     * 
     * @param name 角色名
     * @return 角色对象（Optional包装）
     */
    Optional<Role> findByName(ERole name);
} 