package com.example.biaozhu.repository;

import com.example.biaozhu.entity.Comment;
import com.example.biaozhu.entity.User;
import com.example.biaozhu.entity.Task;
import com.example.biaozhu.entity.Annotation;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 评论数据访问接口
 */
@Repository
public interface CommentRepository extends JpaRepository<Comment, Long> {
    
    /**
     * 根据创建者查找评论
     * @param user 创建者
     * @return 评论列表
     */
    List<Comment> findByUser(User user);
    
    /**
     * 根据任务查找评论
     * @param task 任务
     * @return 评论列表
     */
    List<Comment> findByTask(Task task);
    
    /**
     * 根据标注查找评论
     * @param annotation 标注
     * @return 评论列表
     */
    List<Comment> findByAnnotation(Annotation annotation);
    
    /**
     * 根据父评论查找回复
     * @param parent 父评论
     * @return 回复评论列表
     */
    List<Comment> findByParent(Comment parent);
    
    /**
     * 根据创建时间范围查找评论
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 评论列表
     */
    List<Comment> findByCreatedAtBetween(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 查找包含特定关键词的评论
     * @param keyword 关键词
     * @return 评论列表
     */
    List<Comment> findByContentContaining(String keyword);
    
    /**
     * 分页查询评论
     * @param pageable 分页参数
     * @return 分页评论结果
     */
    Page<Comment> findAll(Pageable pageable);
    
    /**
     * 分页查询特定任务的评论
     * @param task 任务
     * @param pageable 分页参数
     * @return 分页评论结果
     */
    Page<Comment> findByTask(Task task, Pageable pageable);
    
    /**
     * 分页查询特定标注的评论
     * @param annotation 标注
     * @param pageable 分页参数
     * @return 分页评论结果
     */
    Page<Comment> findByAnnotation(Annotation annotation, Pageable pageable);
    
    /**
     * 查找最新的评论
     * @param pageable 分页参数
     * @return 最新评论列表
     */
    @Query("SELECT c FROM Comment c ORDER BY c.createdAt DESC")
    List<Comment> findRecentComments(Pageable pageable);
    
    /**
     * 根据解决状态查找评论
     * @param resolved 是否已解决
     * @return 评论列表
     */
    List<Comment> findByResolved(boolean resolved);
    
    /**
     * 根据任务和解决状态查找评论
     * @param task 任务
     * @param resolved 是否已解决
     * @return 评论列表
     */
    List<Comment> findByTaskAndResolved(Task task, boolean resolved);
    
    /**
     * 统计任务的评论数量
     * @param task 任务
     * @return 评论数量
     */
    long countByTask(Task task);
    
    /**
     * 统计标注的评论数量
     * @param annotation 标注
     * @return 评论数量
     */
    long countByAnnotation(Annotation annotation);
    
    /**
     * 统计未解决的评论数量
     * @return 未解决评论数量
     */
    long countByResolved(boolean resolved);
} 