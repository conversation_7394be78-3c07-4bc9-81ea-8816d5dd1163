package com.example.biaozhu.repository;

import com.example.biaozhu.entity.Project;
import com.example.biaozhu.entity.Project.ProjectStatus;
import com.example.biaozhu.entity.Project.ProjectType;
import com.example.biaozhu.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 项目仓库接口
 */
@Repository
public interface ProjectRepository extends JpaRepository<Project, Long> {
    
    /**
     * 根据创建者查找项目
     * 
     * @param createdBy 创建者
     * @param pageable 分页参数
     * @return 项目分页结果
     */
    Page<Project> findByCreatedBy(User createdBy, Pageable pageable);
    
    /**
     * 根据活动状态查找项目
     * 
     * @param active 活动状态
     * @return 项目列表
     */
    List<Project> findByActive(boolean active);
    
    /**
     * 根据名称模糊查询项目
     * 
     * @param name 项目名称
     * @return 项目列表
     */
    List<Project> findByNameContaining(String name);
    
    /**
     * 根据创建者和活动状态查找项目
     * 
     * @param createdBy 创建者
     * @param active 活动状态
     * @return 项目列表
     */
    List<Project> findByCreatedByAndActive(User createdBy, boolean active);
    
    /**
     * 根据状态查找项目
     * @param status 项目状态
     * @return 项目列表
     */
    List<Project> findByStatus(ProjectStatus status);
    
    /**
     * 根据类型查找项目
     * @param type 项目类型
     * @return 项目列表
     */
    List<Project> findByType(ProjectType type);
    
    /**
     * 查找用户参与的项目
     * @param user 用户
     * @return 项目列表
     */
    @Query("SELECT p FROM Project p JOIN p.members m WHERE m = :user")
    List<Project> findByMember(User user);
    
    /**
     * 查找用户管理的项目
     * @param user 用户
     * @return 项目列表
     */
    @Query("SELECT p FROM Project p JOIN p.managers m WHERE m = :user")
    List<Project> findByManager(User user);
    
    /**
     * 根据名称和描述模糊查询项目（分页）
     * @param keyword 关键词
     * @param pageable 分页参数
     * @return 分页项目结果
     */
    @Query("SELECT p FROM Project p WHERE p.name LIKE %:keyword% OR p.description LIKE %:keyword%")
    Page<Project> searchByKeyword(String keyword, Pageable pageable);
    
    /**
     * 根据进度范围查找项目
     * @param minProgress 最小进度
     * @param maxProgress 最大进度
     * @return 项目列表
     */
    List<Project> findByProgressBetween(double minProgress, double maxProgress);
    
    /**
     * 统计不同状态的项目数量
     * @return 状态和数量的对应关系
     */
    @Query("SELECT p.status, COUNT(p) FROM Project p GROUP BY p.status")
    List<Object[]> countByStatus();
    
    /**
     * 根据名称或描述模糊查询项目（分页）
     * 
     * @param name 项目名称关键词
     * @param description 项目描述关键词
     * @param pageable 分页参数
     * @return 项目分页结果
     */
    Page<Project> findByNameContainingOrDescriptionContaining(String name, String description, Pageable pageable);
} 