package com.example.biaozhu.repository;

import com.example.biaozhu.entity.Label;
import com.example.biaozhu.entity.Project;
import com.example.biaozhu.entity.Dataset;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 标签数据访问接口
 */
@Repository
public interface LabelRepository extends JpaRepository<Label, Long> {
    
    /**
     * 根据名称查找标签
     * @param name 标签名称
     * @return 标签（可选）
     */
    Optional<Label> findByName(String name);
    
    /**
     * 根据名称模糊查找标签
     * @param nameLike 标签名称（模糊匹配）
     * @return 标签列表
     */
    List<Label> findByNameContaining(String nameLike);
    
    /**
     * 根据项目查找标签
     * @param project 项目
     * @return 标签列表
     */
    List<Label> findByProject(Project project);
    
    /**
     * 根据数据集查找标签
     * @param dataset 数据集
     * @return 标签列表
     */
    List<Label> findByDataset(Dataset dataset);
    
    /**
     * 根据颜色查找标签
     * @param color 颜色代码
     * @return 标签列表
     */
    List<Label> findByColor(String color);
    
    /**
     * 根据类别查找标签
     * @param category 标签类别
     * @return 标签列表
     */
    List<Label> findByCategory(String category);
    
    /**
     * 查找指定项目中特定类别的标签
     * @param project 项目
     * @param category 标签类别
     * @return 标签列表
     */
    List<Label> findByProjectAndCategory(Project project, String category);
    
    /**
     * 查找激活状态的标签
     * @param active 是否激活
     * @return 标签列表
     */
    List<Label> findByActive(boolean active);
    
    /**
     * 查找指定项目中的激活标签
     * @param project 项目
     * @param active 是否激活
     * @return 标签列表
     */
    List<Label> findByProjectAndActive(Project project, boolean active);
    
    /**
     * 分页查询标签
     * @param pageable 分页参数
     * @return 分页标签结果
     */
    Page<Label> findAll(Pageable pageable);
    
    /**
     * 分页查询指定项目的标签
     * @param project 项目
     * @param pageable 分页参数
     * @return 分页标签结果
     */
    Page<Label> findByProject(Project project, Pageable pageable);
    
    /**
     * 根据热度降序排列标签
     * @return 标签列表
     */
    @Query("SELECT l FROM Label l ORDER BY l.usageCount DESC")
    List<Label> findAllOrderByUsageCountDesc();
    
    /**
     * 统计项目的标签数量
     * @param project 项目
     * @return 标签数量
     */
    long countByProject(Project project);
    
    /**
     * 统计各类别的标签数量
     * @return 类别和数量的对应关系
     */
    @Query("SELECT l.category, COUNT(l) FROM Label l GROUP BY l.category")
    List<Object[]> countByCategory();
    
    /**
     * 根据项目和名称查找标签
     * @param project 项目
     * @param name 标签名称
     * @return 标签（可选）
     */
    Optional<Label> findByProjectAndName(Project project, String name);
} 